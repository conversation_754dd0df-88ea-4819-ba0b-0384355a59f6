import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
import os
from datetime import datetime
from typing import Optional
warnings.filterwarnings('ignore')

# 配置matplotlib中文字体
import matplotlib
import platform

# 全局设置，解决负号显示问题
matplotlib.rcParams['axes.unicode_minus'] = False
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
# 设置更多字体相关参数
matplotlib.rcParams['font.family'] = 'sans-serif'
matplotlib.rcParams['mathtext.fontset'] = 'stix'
matplotlib.rcParams['mathtext.default'] = 'regular'

def setup_chinese_font():
    """配置matplotlib中文字体"""
    # 设置全局字体配置，解决负号显示问题
    matplotlib.rcParams['axes.unicode_minus'] = False
    matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']

    system = platform.system()
    if system == "Windows":
        fonts = ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi']
    elif system == "Darwin":  # macOS
        fonts = ['Arial Unicode MS', 'Heiti TC', 'STHeiti']
    else:  # Linux
        fonts = ['DejaVu Sans', 'WenQuanYi Micro Hei', 'SimHei']

    for font in fonts:
        try:
            matplotlib.rcParams['font.sans-serif'] = [font, 'DejaVu Sans']
            matplotlib.rcParams['axes.unicode_minus'] = False
            # 测试字体是否可用
            fig, ax = plt.subplots(figsize=(1, 1))
            ax.text(0.5, 0.5, '测试', fontsize=12)
            plt.close(fig)
            print(f"使用字体: {font}")
            break
        except:
            continue
    else:
        print("警告: 未找到合适的中文字体，中文可能显示为方框")
        matplotlib.rcParams['font.sans-serif'] = ['DejaVu Sans']
        matplotlib.rcParams['axes.unicode_minus'] = False

# 初始化字体配置
setup_chinese_font()

def ensure_outputs_dir():
    """确保outputs文件夹存在"""
    # 动态获取当前脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    outputs_dir = os.path.join(script_dir, "outputs")

    if not os.path.exists(outputs_dir):
        os.makedirs(outputs_dir)

    subdirs = ["plots", "data", "models", "predictions"]
    for subdir in subdirs:
        subdir_path = os.path.join(outputs_dir, subdir)
        if not os.path.exists(subdir_path):
            os.makedirs(subdir_path)

    return outputs_dir

class RTDataAnalyzer:
    """RT数据分析器 - PyTorch版本"""

    def __init__(self):
        self.df: Optional[pd.DataFrame] = None
        self.analysis_results = {}
        ensure_outputs_dir()
    
    def analyze_rt_data(self, df: pd.DataFrame, save_plots: bool = True,
                       output_dir: Optional[str] = None, show_plots: bool = False):
        """
        执行完整的RT数据分析
        
        Args:
            df: 要分析的数据
            save_plots: 是否保存图表
            output_dir: 图表保存目录
            show_plots: 是否显示图表
        """
        self.df = df.copy()

        print("🔍 开始RT数据分析...")

        # 确保输出目录存在
        if save_plots:
            if output_dir is None:
                # 使用动态路径
                script_dir = os.path.dirname(os.path.abspath(__file__))
                output_dir = os.path.join(script_dir, "outputs", "plots")
            os.makedirs(output_dir, exist_ok=True)

        # 确保 output_dir 不为 None
        if output_dir is None:
            script_dir = os.path.dirname(os.path.abspath(__file__))
            output_dir = os.path.join(script_dir, "outputs", "plots")

        # 1. 基础统计分析
        self._basic_statistics()

        # 2. 数据分布分析
        self._distribution_analysis(save_plots, output_dir, show_plots)
        
        # 3. 相关性分析
        self._correlation_analysis(save_plots, output_dir, show_plots)
        
        # 4. 分类变量分析
        self._categorical_analysis(save_plots, output_dir, show_plots)
        
        # 5. 异常值检测
        self._outlier_detection(save_plots, output_dir, show_plots)
        
        print("✅ 数据分析完成")
        
        return self.analysis_results
    
    def _basic_statistics(self):
        """基础统计分析"""
        print("\n📊 基础统计信息:")
        
        # 数据概览
        print(f"数据形状: {self.df.shape}")
        print(f"数据类型:\n{self.df.dtypes}")
        
        # 数值变量统计
        numeric_cols = ['qty', 'rt']
        stats_summary = self.df[numeric_cols].describe()
        print(f"\n数值变量统计:\n{stats_summary}")
        
        # 分类变量统计
        categorical_cols = ['equip', 'sub_equip', 'capability', 'recipe']
        for col in categorical_cols:
            unique_count = self.df[col].nunique()
            print(f"{col}: {unique_count} 个唯一值")
        
        # 保存统计结果
        self.analysis_results['basic_stats'] = {
            'shape': self.df.shape,
            'numeric_summary': stats_summary.to_dict(),
            'categorical_counts': {col: self.df[col].nunique() for col in categorical_cols}
        }
    
    def _distribution_analysis(self, save_plots: bool, output_dir: str, show_plots: bool):
        """数据分布分析"""
        print("\n📈 数据分布分析...")

        # 确保字体配置生效
        matplotlib.rcParams['axes.unicode_minus'] = False

        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # RT分布
        axes[0, 0].hist(self.df['rt'], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0, 0].set_title('RT分布')
        axes[0, 0].set_xlabel('运行时间 (小时)')
        axes[0, 0].set_ylabel('频次')
        
        # QTY分布
        axes[0, 1].hist(self.df['qty'], bins=30, alpha=0.7, color='lightgreen', edgecolor='black')
        axes[0, 1].set_title('QTY分布')
        axes[0, 1].set_xlabel('数量 (片)')
        axes[0, 1].set_ylabel('频次')
        
        # RT箱线图
        axes[1, 0].boxplot(self.df['rt'])
        axes[1, 0].set_title('RT箱线图')
        axes[1, 0].set_ylabel('运行时间 (小时)')
        
        # QTY vs RT散点图
        axes[1, 1].scatter(self.df['qty'], self.df['rt'], alpha=0.6, color='coral')
        axes[1, 1].set_title('QTY vs RT散点图')
        axes[1, 1].set_xlabel('数量 (片)')
        axes[1, 1].set_ylabel('运行时间 (小时)')
        
        plt.tight_layout()
        
        if save_plots:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            plot_path = os.path.join(output_dir, f"distribution_analysis_{timestamp}.png")
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            print(f"分布分析图已保存到: {plot_path}")
        
        if show_plots:
            plt.show()
        else:
            plt.close()
    
    def _correlation_analysis(self, save_plots: bool, output_dir: str, show_plots: bool):
        """相关性分析"""
        print("\n🔗 相关性分析...")
        
        # 数值变量相关性
        numeric_df = self.df[['qty', 'rt']].copy()
        
        # 添加编码后的分类变量
        from sklearn.preprocessing import LabelEncoder
        categorical_cols = ['equip', 'sub_equip', 'capability', 'recipe']
        
        for col in categorical_cols:
            le = LabelEncoder()
            numeric_df[f'{col}_encoded'] = le.fit_transform(self.df[col])
        
        # 计算相关性矩阵
        correlation_matrix = numeric_df.corr()
        
        # 绘制相关性热图
        # 确保字体配置生效
        matplotlib.rcParams['axes.unicode_minus'] = False

        plt.figure(figsize=(12, 10))
        sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0,
                   square=True, linewidths=0.5)
        plt.title('特征相关性热图')
        plt.tight_layout()
        
        if save_plots:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            plot_path = os.path.join(output_dir, f"correlation_analysis_{timestamp}.png")
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            print(f"相关性分析图已保存到: {plot_path}")
        
        if show_plots:
            plt.show()
        else:
            plt.close()
        
        # 保存相关性结果
        self.analysis_results['correlation'] = correlation_matrix.to_dict()
    
    def _categorical_analysis(self, save_plots: bool, output_dir: str, show_plots: bool):
        """分类变量分析"""
        print("\n📋 分类变量分析...")

        # 确保字体配置生效
        matplotlib.rcParams['axes.unicode_minus'] = False

        categorical_cols = ['equip', 'sub_equip', 'capability', 'recipe']

        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        axes = axes.flatten()
        
        for i, col in enumerate(categorical_cols):
            # 计算每个类别的平均RT
            avg_rt = self.df.groupby(col)['rt'].mean().sort_values(ascending=False)
            
            # 绘制条形图
            avg_rt.plot(kind='bar', ax=axes[i], color='steelblue', alpha=0.8)
            axes[i].set_title(f'{col.upper()} 平均RT')
            axes[i].set_xlabel(col.upper())
            axes[i].set_ylabel('平均运行时间 (小时)')
            axes[i].tick_params(axis='x', rotation=45)
            
            # 保存分析结果
            self.analysis_results[f'{col}_avg_rt'] = avg_rt.to_dict()
        
        plt.tight_layout()
        
        if save_plots:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            plot_path = os.path.join(output_dir, f"categorical_analysis_{timestamp}.png")
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            print(f"分类变量分析图已保存到: {plot_path}")
        
        if show_plots:
            plt.show()
        else:
            plt.close()
    
    def _outlier_detection(self, save_plots: bool, output_dir: str, show_plots: bool):
        """异常值检测"""
        print("\n🎯 异常值检测...")
        
        # 使用IQR方法检测异常值
        Q1_rt = self.df['rt'].quantile(0.25)
        Q3_rt = self.df['rt'].quantile(0.75)
        IQR_rt = Q3_rt - Q1_rt
        
        Q1_qty = self.df['qty'].quantile(0.25)
        Q3_qty = self.df['qty'].quantile(0.75)
        IQR_qty = Q3_qty - Q1_qty
        
        # 定义异常值边界
        rt_lower = Q1_rt - 1.5 * IQR_rt
        rt_upper = Q3_rt + 1.5 * IQR_rt
        qty_lower = Q1_qty - 1.5 * IQR_qty
        qty_upper = Q3_qty + 1.5 * IQR_qty
        
        # 识别异常值
        rt_outliers = (self.df['rt'] < rt_lower) | (self.df['rt'] > rt_upper)
        qty_outliers = (self.df['qty'] < qty_lower) | (self.df['qty'] > qty_upper)
        
        print(f"RT异常值数量: {rt_outliers.sum()}")
        print(f"QTY异常值数量: {qty_outliers.sum()}")
        
        # 绘制异常值检测图
        # 确保字体配置生效
        matplotlib.rcParams['axes.unicode_minus'] = False

        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        
        # RT异常值
        axes[0].scatter(self.df.index[~rt_outliers], self.df.loc[~rt_outliers, 'rt'], 
                       alpha=0.6, color='blue', label='正常值')
        axes[0].scatter(self.df.index[rt_outliers], self.df.loc[rt_outliers, 'rt'], 
                       alpha=0.8, color='red', label='异常值')
        axes[0].set_title('RT异常值检测')
        axes[0].set_xlabel('数据索引')
        axes[0].set_ylabel('运行时间 (小时)')
        axes[0].legend()
        
        # QTY异常值
        axes[1].scatter(self.df.index[~qty_outliers], self.df.loc[~qty_outliers, 'qty'], 
                       alpha=0.6, color='green', label='正常值')
        axes[1].scatter(self.df.index[qty_outliers], self.df.loc[qty_outliers, 'qty'], 
                       alpha=0.8, color='red', label='异常值')
        axes[1].set_title('QTY异常值检测')
        axes[1].set_xlabel('数据索引')
        axes[1].set_ylabel('数量 (片)')
        axes[1].legend()
        
        plt.tight_layout()
        
        if save_plots:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            plot_path = os.path.join(output_dir, f"outlier_detection_{timestamp}.png")
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            print(f"异常值检测图已保存到: {plot_path}")
        
        if show_plots:
            plt.show()
        else:
            plt.close()
        
        # 保存异常值检测结果
        self.analysis_results['outliers'] = {
            'rt_outliers_count': rt_outliers.sum(),
            'qty_outliers_count': qty_outliers.sum(),
            'rt_boundaries': {'lower': rt_lower, 'upper': rt_upper},
            'qty_boundaries': {'lower': qty_lower, 'upper': qty_upper}
        }
    
    def recommend_modeling_strategy(self) -> str:
        """基于数据分析结果推荐建模策略"""
        if not self.analysis_results:
            return "请先运行数据分析"
        
        data_size = self.analysis_results['basic_stats']['shape'][0]
        
        if data_size < 100:
            strategy = "数据量较小，建议使用简单模型或收集更多数据"
        elif data_size < 1000:
            strategy = "数据量中等，建议使用中等复杂度的神经网络"
        else:
            strategy = "数据量充足，可以使用复杂的深度学习模型"
        
        return strategy

def analyze_rt_data(df: pd.DataFrame, save_plots: bool = True, 
                   output_dir: str = "outputs/plots", show_plots: bool = False):
    """
    便捷函数：执行RT数据分析
    
    Args:
        df: 要分析的数据
        save_plots: 是否保存图表
        output_dir: 图表保存目录
        show_plots: 是否显示图表
    """
    analyzer = RTDataAnalyzer()
    return analyzer.analyze_rt_data(df, save_plots, output_dir, show_plots)

if __name__ == "__main__":
    # 测试数据分析功能
    from rt_prediction_model_pytorch import generate_sample_data
    
    print("=== 测试数据分析功能 ===")
    
    # 生成测试数据
    df = generate_sample_data(500)
    
    # 执行分析
    analyzer = RTDataAnalyzer()
    results = analyzer.analyze_rt_data(df, save_plots=True, show_plots=False)
    
    # 获取建模建议
    strategy = analyzer.recommend_modeling_strategy()
    print(f"\n建模策略建议: {strategy}")
    
    print("\n=== 分析完成 ===")
